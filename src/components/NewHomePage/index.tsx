"use client";
import React from "react";
import WhyPeopleChoose from "@/components/NewHomePage/components/WhyPeopleChoose";
import homePageData from "@/components/NewHomePage/components/data";
import HowOneAssureWork from "@/components/NewHomePage/components/HowOneAssureWork";
import AskAnythingSection from "@/components/NewHomePage/components/AskAnythingSection";
import HeroSection from "@/components/NewHomePage/components/HeroSection";
import HandBorder from "@/components/NewHomePage/components/HandBorder";
// import AskAnythingSection from "./components/AskAnythingSection";
import AskAnythingSectio from "./components/AskAnythingSection";
import CheckSection from "./components/AskAnythingSection";
import TalkToHuman from "./components/TalkToHuman";
import MeetOurTeam from "./components/MeetOurTeam";

const index = () => {
  return (
    <>
      <HandBorder />
      <HeroSection />
      <WhyPeopleChoose
        pill_Content={homePageData.whyToChooseUs.pill_Content}
        title={homePageData.whyToChooseUs.title}
        cards={homePageData.whyToChooseUs.cards}
      />
      <HowOneAssureWork
        pill={homePageData.howOneAssureWork.pill}
        heading={homePageData.howOneAssureWork.heading}
        steps={homePageData.howOneAssureWork.steps}
      />
      {/* <AskAnythingSection
        pill={homePageData.askAnythingSection.pill}
        heading={homePageData.askAnythingSection.heading}
        subheading={homePageData.askAnythingSection.subheading}
      /> */}
      <AskAnythingSectio
          pill={homePageData.askAnythingSection.pill}
        heading={homePageData.askAnythingSection.heading}
        subheading={homePageData.askAnythingSection.subheading}
      
      />
      <TalkToHuman />
      <MeetOurTeam />
    </>
  );
};

export default index;
